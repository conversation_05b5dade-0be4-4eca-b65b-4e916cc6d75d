# 📚 Documentación del Sistema de Facturación

## 🎯 Resumen del Sistema

Sistema de facturación con arquitectura hexagonal que integra ventas, comprobantes fiscales AFIP, impresión térmica y reportes. Diseñado para comercios que requieren cumplimiento fiscal argentino con operación online y offline.

## 📋 Índice de Documentación

### 🏗️ **Arquitectura y Resumen General**
- **[📄 IMPLEMENTATION_SUMMARY.md](IMPLEMENTATION_SUMMARY.md)** - Resumen completo de funcionalidades implementadas, componentes principales y arquitectura del sistema

### 🛒 **Ventas**
- **[📄 VENTAS_API.md](VENTAS_API.md)** - API completa de ventas: creación, consulta, filtros, paginación y cancelación

### 🧾 **Comprobantes Fiscales**
- **[📄 COMPROBANTES_FISCALES.md](COMPROBANTES_FISCALES.md)** - Sistema completo de comprobantes: CAE, CAEA, numeración independiente y manejo de errores

### 🏛️ **Integración AFIP**
- **[📄 INTEGRACION_AFIP.md](INTEGRACION_AFIP.md)** - Integración con servicios AFIP: WSAA, WSFE, CAEA y automatización

### 📊 **Reportes**
- **[📄 REPORTS_API_DOCUMENTATION.md](REPORTS_API_DOCUMENTATION.md)** - API de reportes unificada con filtros, estadísticas y alertas

### 🖨️ **Impresión Térmica**
- **[📄 IMPRESION_TERMICA.md](IMPRESION_TERMICA.md)** - Sistema de impresión: tickets fiscales, estilos ESC/POS y códigos QR

### 📦 **Productos y Categorías**
- **[📄 PRODUCTOS_Y_CATEGORIAS.md](PRODUCTOS_Y_CATEGORIAS.md)** - Gestión de productos y categorías: CRUD, actualizaciones masivas y sincronización

## 🚀 Inicio Rápido

### 1. **Configuración Inicial**
```bash
# Copiar variables de entorno
cp .env.example .env

# Configurar base de datos y AFIP
nano .env
```

### 2. **Certificados AFIP**
```bash
# Crear directorio de certificados
mkdir certificados

# Copiar certificado y clave privada
cp certificado.pem certificados/
cp clave_privada.pem certificados/
```

### 3. **Ejecutar Aplicación**
```bash
# Compilar y ejecutar
./gradlew run

# La aplicación estará disponible en:
# http://localhost:8080
```

## 🌐 Endpoints Principales

### Ventas
- `POST /api/sales` - Crear venta
- `GET /api/sales` - Buscar ventas con filtros
- `GET /api/sales/{id}` - Consultar venta específica
- `POST /api/sales/{id}/cancel` - Cancelar venta

### Comprobantes
- `POST /api/comprobantes` - Generar comprobante fiscal
- `GET /api/comprobantes/venta/{id}` - Comprobantes de una venta
- `GET /api/comprobantes/estadisticas` - Estadísticas de comprobantes

### Reportes
- `GET /api/reports/sales` - Reporte completo de ventas

### Impresión
- `POST /api/print/ticket/{id}` - Imprimir ticket de venta
- `POST /api/print/reporte-ventas` - Imprimir reporte de ventas

### Productos y Categorías
- `GET /api/productos` - Listar productos
- `PUT /api/productos/bulk` - Actualización masiva de productos
- `GET /api/categorias` - Listar categorías
- `PUT /api/categorias/bulk` - Actualización masiva de categorías

### CAEA
- `GET /api/caea` - Listar códigos CAEA
- `POST /api/caea/solicitar` - Solicitar nuevo código CAEA

## 🔧 Configuración Clave

### Variables de Entorno Esenciales
```env
# Base de datos
DATABASE_URL=*******************************************
DATABASE_USERNAME=usuario
DATABASE_PASSWORD=password

# AFIP
AFIP_CUIT=20123456789
AFIP_PUNTO_VENTA_CAE=1
AFIP_PUNTO_VENTA_CAEA=2
AFIP_CERTIFICATE_PATH=certificados/certificado.pem
AFIP_PRIVATE_KEY_PATH=certificados/clave_privada.pem

# Empresa
EMPRESA_NOMBRE=Mi Comercio
EMPRESA_DIRECCION=Av. Principal 123
EMPRESA_CUIT=20123456789

# Impresora
PRINTER_NAME=POS-80
PRINTER_CONNECTION_TYPE=USB
```

## 🎯 Características Destacadas

### ✅ **Arquitectura Hexagonal**
- Separación clara entre dominio, aplicación e infraestructura
- Puertos y adaptadores bien definidos
- Fácil testing y mantenimiento

### ✅ **Cumplimiento Fiscal AFIP**
- Comprobantes tipo B (Factura, Nota de Crédito, Nota de Débito)
- Autorización online (CAE) y offline (CAEA)
- Códigos QR fiscales según especificación ARCA
- Numeración independiente por tipo de comprobante

### ✅ **Operación Robusta**
- Manejo de errores sin bloqueo de operaciones
- Fallback automático CAE → CAEA
- Impresión no bloquea ventas
- Logs detallados para debugging

### ✅ **API REST Completa**
- Endpoints unificados y consistentes
- Filtros avanzados y paginación
- Validaciones robustas
- Documentación completa

### ✅ **Reportes Inteligentes**
- Totales correctos (sin ventas canceladas)
- Información de canceladas como alertas
- Filtros flexibles por período, usuario, medio de pago
- Estadísticas fiscales y de negocio

## 🚨 Consideraciones Importantes

### Ambientes AFIP
- **Homologación**: Para testing y desarrollo
- **Producción**: Para uso real con clientes
- URLs y certificados diferentes según ambiente

### Backup y Seguridad
- **Certificados AFIP**: Backup seguro y rotación
- **Base de datos**: Backup regular de transacciones
- **Logs**: Retención para auditoría fiscal

### Performance
- **Conexiones AFIP**: Pool de conexiones para alta concurrencia
- **Base de datos**: Índices optimizados para consultas frecuentes
- **Impresión**: Procesamiento asíncrono

## 🔍 Troubleshooting Rápido

### Problemas Comunes
1. **Error de certificados AFIP**: Verificar formato PEM y vencimiento
2. **Impresora no responde**: Comprobar conexión y drivers
3. **Numeración duplicada**: Ejecutar sincronización con AFIP
4. **Servicios AFIP no disponibles**: Usar modo CAEA offline

### Logs Importantes
```bash
# Ver logs de AFIP
tail -f logs/afip.log

# Ver logs de impresión
tail -f logs/printer.log

# Ver logs de aplicación
tail -f logs/application.log
```

## 📞 Soporte

### Documentación Adicional
- Cada archivo de documentación incluye ejemplos detallados
- Casos de uso comunes documentados
- Troubleshooting específico por módulo

### Estructura del Código
```
src/main/kotlin/com/gnico/majo/
├── application/          # Casos de uso y puertos
├── adapter/             # Adaptadores (REST, DB, AFIP, Printer)
├── infrastructure/      # Configuración e infraestructura
└── jooq/               # Clases generadas por JOOQ
```

---