# 🏛️ Integración AFIP - Documentación Completa

## 📋 Resumen

Sistema completo de integración con servicios AFIP para autorización de comprobantes fiscales, incluyendo CAE (online), CAEA (offline) y automatización de códigos CAEA.

## 🎯 Características Principales

### ✅ **Servicios AFIP Integrados**
- **WSAA**: Autenticación y autorización
- **WSFE**: Facturación electrónica (CAE)
- **CAEA**: Códigos de autorización anticipada

### ✅ **Modos de Autorización**
- **CAE Online**: Autorización inmediata con conectividad
- **CAEA Offline**: Códigos pre-aprobados para uso sin internet
- **Fallback automático**: CAE → CAEA en caso de falla

### ✅ **Automatización CAEA**
- **Solicitud automática**: Códigos para próximos períodos
- **Renovación programada**: Antes del vencimiento
- **Gestión de stock**: Control de códigos disponibles

## 🌐 Servicios Web AFIP

### WSAA (Web Service de Autenticación y Autorización)

#### Propósito
Obtiene tickets de acceso (TA) para autorizar el uso de otros servicios AFIP.

#### Configuración
```env
AFIP_WSAA_URL=https://wsaahomo.afip.gov.ar/ws/services/LoginCms
AFIP_CERTIFICATE_PATH=certificados/certificado.pem
AFIP_PRIVATE_KEY_PATH=certificados/clave_privada.pem
AFIP_CUIT=20123456789
```

#### Flujo de Autenticación
1. **Generar TRA**: Ticket de Requerimiento de Acceso
2. **Firmar TRA**: Con certificado digital
3. **Enviar a WSAA**: Obtener Ticket de Acceso (TA)
4. **Usar TA**: Para autorizar llamadas a WSFE

### WSFE (Web Service de Facturación Electrónica)

#### Propósito
Autoriza comprobantes fiscales y obtiene CAE (Código de Autorización Electrónica).

#### Configuración
```env
AFIP_WSFE_URL=https://wswhomo.afip.gov.ar/wsfev1/service.asmx
AFIP_PUNTO_VENTA_CAE=1
```

#### Tipos de Comprobante Soportados
- **Factura B** (Código 6): Consumidor final
- **Nota de Crédito B** (Código 8): Devoluciones/cancelaciones
- **Nota de Débito B** (Código 7): Ajustes adicionales

#### Flujo de Autorización CAE
1. **Consultar último número**: Obtener próximo número de comprobante
2. **Preparar solicitud**: Datos del comprobante
3. **Enviar a WSFE**: Solicitar autorización
4. **Recibir CAE**: Código y fecha de vencimiento
5. **Guardar comprobante**: Con datos de autorización

## 🔄 Sistema CAEA

### ¿Qué es CAEA?
**Código de Autorización Electrónica Anticipada**: Permite emitir comprobantes sin conectividad inmediata con AFIP.

### Características
- **Períodos quincenal**: Válidos por 15 días
- **Solicitud anticipada**: Se piden antes del período de uso
- **Punto de venta específico**: Diferente al CAE
- **Numeración independiente**: Por tipo de comprobante

### Configuración CAEA
```env
AFIP_PUNTO_VENTA_CAEA=2
CAEA_AUTO_REQUEST_ENABLED=true
CAEA_REQUEST_DAYS_AHEAD=5
```

### Automatización CAEA

#### Solicitud Automática
El sistema solicita automáticamente códigos CAEA para próximos períodos:

```kotlin
// Ejecuta diariamente
@Scheduled(cron = "0 0 2 * * *") // 2:00 AM
fun solicitarCaeaAutomatico() {
    val proximoPeriodo = calcularProximoPeriodo()
    if (debesolicitarCaea(proximoPeriodo)) {
        solicitarCaea(proximoPeriodo)
    }
}
```

#### Cálculo de Períodos
- **Primera quincena**: 1-15 del mes
- **Segunda quincena**: 16-último día del mes
- **Solicitud anticipada**: 5 días antes del período

#### API Endpoints CAEA

##### Listar Códigos CAEA
**Endpoint:** `GET /api/caea`

**Response:**
```json
[
  {
    "id": 1,
    "codigo": "24251234567890",
    "periodo": "202401-1",
    "fechaDesde": "2024-01-01",
    "fechaHasta": "2024-01-15",
    "fechaVencimiento": "2024-01-25",
    "estado": "ACTIVO",
    "ultimoNumeroFacturaB": 0,
    "ultimoNumeroNotaCreditoB": 0,
    "ultimoNumeroNotaDebitoB": 0
  }
]
```

##### Solicitar Nuevo CAEA
**Endpoint:** `POST /api/caea/solicitar`

**Request Body:**
```json
{
  "periodo": "202401-2"
}
```

##### Consultar Estado en AFIP
**Endpoint:** `GET /api/caea/{id}/consultar-afip`

Verifica el estado actual del código CAEA en AFIP.

## 🔧 Configuración de Certificados

### Requisitos
- **Certificado digital**: Emitido por AFIP
- **Clave privada**: Correspondiente al certificado
- **Formato PEM**: Ambos archivos en formato PEM

### Estructura de Archivos
```
certificados/
├── certificado.pem
├── clave_privada.pem
└── README.md
```

### Generación de Certificados (Testing)
```bash
# Generar clave privada
openssl genrsa -out clave_privada.pem 2048

# Generar solicitud de certificado
openssl req -new -key clave_privada.pem -out solicitud.csr

# Subir solicitud.csr a AFIP para obtener certificado.pem
```

## 📊 Numeración de Comprobantes

### Numeración Independiente por Tipo
Cada tipo de comprobante mantiene su propia secuencia:

```sql
-- Tabla de numeración local
CREATE TABLE comprobante_numeracion (
    id SERIAL PRIMARY KEY,
    punto_venta INTEGER NOT NULL,
    tipo_comprobante VARCHAR(20) NOT NULL,
    ultimo_numero INTEGER NOT NULL DEFAULT 0,
    UNIQUE(punto_venta, tipo_comprobante)
);
```

### Sincronización con AFIP
- **Consulta automática**: Último número usado en AFIP
- **Actualización local**: Si AFIP tiene números mayores
- **Prevención de conflictos**: Locks pesimistas en operaciones

### Flujo de Numeración
1. **Obtener número local**: De tabla `comprobante_numeracion`
2. **Incrementar y reservar**: Operación atómica
3. **Usar en comprobante**: Número garantizado único
4. **Sincronizar con AFIP**: Si hay diferencias

## 🚨 Manejo de Errores

### Errores Comunes AFIP

#### Autenticación
- **Certificado vencido**: Renovar certificado
- **Clave incorrecta**: Verificar clave privada
- **CUIT no autorizado**: Verificar habilitación en AFIP

#### Facturación
- **Número duplicado**: Sincronizar numeración
- **Datos inválidos**: Verificar formato de campos
- **Servicio no disponible**: Usar CAEA como fallback

#### CAEA
- **Período inválido**: Verificar fechas del período
- **Código vencido**: Solicitar nuevo código
- **Sin códigos disponibles**: Activar solicitud automática

### Estrategias de Recuperación
1. **Reintentos automáticos**: Con backoff exponencial
2. **Fallback CAE → CAEA**: En caso de falla de conectividad
3. **Logs detallados**: Para debugging y auditoría
4. **Alertas**: Notificación de errores críticos

## 📈 Monitoreo y Alertas

### Métricas Importantes
- **Tasa de éxito CAE**: Porcentaje de autorizaciones exitosas
- **Tiempo de respuesta AFIP**: Latencia de servicios
- **Stock de códigos CAEA**: Códigos disponibles
- **Certificados próximos a vencer**: Alertas tempranas

### Dashboard de Estado
```json
{
  "serviciosAfip": {
    "wsaa": "ACTIVO",
    "wsfe": "ACTIVO",
    "ultimaActualizacion": "2024-01-15T14:30:00"
  },
  "certificados": {
    "estado": "VALIDO",
    "vencimiento": "2024-12-31",
    "diasRestantes": 350
  },
  "caea": {
    "codigosDisponibles": 3,
    "proximoVencimiento": "2024-01-25",
    "solicitudAutomatica": true
  }
}
```

## 🔍 Troubleshooting

### Problemas de Conectividad
1. **Verificar URLs**: Homologación vs Producción
2. **Firewall**: Puertos 80/443 abiertos
3. **DNS**: Resolución de dominios AFIP
4. **Proxy**: Configuración si aplica

### Problemas de Certificados
1. **Formato**: Verificar formato PEM
2. **Correspondencia**: Certificado y clave deben coincidir
3. **Vencimiento**: Renovar antes del vencimiento
4. **Permisos**: Archivos legibles por la aplicación

### Problemas de Numeración
1. **Sincronización**: Ejecutar sincronización manual
2. **Conflictos**: Verificar locks en base de datos
3. **Diferencias**: Comparar con último número AFIP

## ⚙️ Configuración Completa

### Variables de Entorno
```env
# URLs AFIP (Homologación)
AFIP_WSAA_URL=https://wsaahomo.afip.gov.ar/ws/services/LoginCms
AFIP_WSFE_URL=https://wswhomo.afip.gov.ar/wsfev1/service.asmx

# URLs AFIP (Producción)
# AFIP_WSAA_URL=https://wsaa.afip.gov.ar/ws/services/LoginCms
# AFIP_WSFE_URL=https://servicios1.afip.gov.ar/wsfev1/service.asmx

# Certificados
AFIP_CERTIFICATE_PATH=certificados/certificado.pem
AFIP_PRIVATE_KEY_PATH=certificados/clave_privada.pem
AFIP_CUIT=20123456789

# Puntos de venta
AFIP_PUNTO_VENTA_CAE=1
AFIP_PUNTO_VENTA_CAEA=2

# CAEA Automatización
CAEA_AUTO_REQUEST_ENABLED=true
CAEA_REQUEST_DAYS_AHEAD=5

# Timeouts y reintentos
AFIP_TIMEOUT_MS=30000
AFIP_MAX_RETRIES=3
```

### Inicialización del Sistema
```kotlin
@Component
class AfipStartupService {
    
    @EventListener(ApplicationReadyEvent::class)
    fun inicializarAfip() {
        // 1. Validar certificados
        validarCertificados()
        
        // 2. Obtener ticket de acceso
        obtenerTicketAcceso()
        
        // 3. Verificar servicios AFIP
        verificarServicios()
        
        // 4. Solicitar CAEA si es necesario
        verificarYSolicitarCaea()
    }
}
```
