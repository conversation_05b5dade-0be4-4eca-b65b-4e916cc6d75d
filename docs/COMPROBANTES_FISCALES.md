# 🧾 Comprobantes Fiscales - Documentación Completa

## 📋 Resumen

El sistema de comprobantes fiscales permite generar facturas, notas de crédito y notas de débito tipo B (consumidor final) tanto online (CAE) como offline (CAEA), con numeración independiente y manejo robusto de errores.

## 🎯 Características Principales

### ✅ **Tipos de Comprobante Soportados**
- **Factura B** - Para ventas a consumidor final
- **Nota de Crédito B** - Para cancelaciones y devoluciones
- **Nota de Débito B** - Para ajustes adicionales

### ✅ **Modos de Autorización**
- **Online (CAE)** - Autorización inmediata con AFIP
- **Offline (CAEA)** - Autorización con códigos pre-aprobados

### ✅ **Numeración Independiente**
- Cada tipo de comprobante tiene su propia secuencia numérica
- Numeración local sincronizada con AFIP
- Prevención de conflictos en concurrencia

## 🌐 API Endpoints

### Generar Comprobante
**Endpoint:** `POST /api/comprobantes`

**Request Body:**
```json
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",
  "modoGeneracion": "AUTO"
}
```

**Parámetros:**
- `ventaId` (requerido): ID de la venta
- `tipoComprobante` (requerido): `FACTURA_B`, `NOTA_CREDITO_B`, `NOTA_DEBITO_B`
- `modoGeneracion` (opcional): `AUTO`, `ONLINE`, `OFFLINE` (default: `AUTO`)

**Response:**
```json
{
  "comprobanteId": 456,
  "numeroComprobante": 123,
  "cae": "74251234567890",
  "fechaVencimientoCae": "2025-08-27",
  "tipoAutorizacion": "CAE"
}
```

### Buscar Comprobantes
**Endpoint:** `GET /api/comprobantes/venta/{ventaId}`

Obtiene todos los comprobantes asociados a una venta.

### Estadísticas
**Endpoint:** `GET /api/comprobantes/estadisticas`

Obtiene estadísticas de ventas sin comprobante y otros datos útiles.

## 🔧 Modos de Generación

### AUTO (Recomendado)
- **Para facturas**: Genera online (CAE) por defecto
- **Para notas de crédito/débito**: Respeta el método del comprobante original
- **Fallback**: Si falla online, intenta offline automáticamente

### ONLINE
- Fuerza generación con CAE
- Requiere conectividad con AFIP
- Autorización inmediata

### OFFLINE
- Fuerza generación con CAEA
- Usa códigos pre-aprobados
- No requiere conectividad inmediata

## 📊 Numeración Independiente

### Problema Resuelto
Antes: Todos los comprobantes compartían la misma numeración, causando conflictos.

Ahora: Cada tipo tiene su propia secuencia:
- Factura B: 1, 2, 3, 4...
- Nota de Crédito B: 1, 2, 3, 4...
- Nota de Débito B: 1, 2, 3, 4...

### Implementación
- **Tabla local**: `comprobante_numeracion` trackea el último número por tipo
- **Operaciones atómicas**: Locks pesimistas previenen conflictos
- **Sincronización**: Automática con AFIP cuando hay diferencias

## 🚨 Manejo de Errores

### Intentos Fallidos
- Se registran todos los intentos (exitosos y fallidos)
- Información detallada de errores para debugging
- Endpoint para consultar intentos: `GET /api/comprobantes/intentos/{ventaId}`

### Recuperación Automática
- Sincronización de numeración con AFIP
- Detección de inconsistencias
- Corrección automática de numeración local

## 🔗 Integración con Ventas

### Cancelación de Ventas
- Las ventas canceladas con comprobante generan automáticamente Nota de Crédito B
- El método de autorización (CAE/CAEA) se mantiene consistente
- Campo `notaCreditoGenerada` trackea el estado

### Alertas en Reportes
- Ventas canceladas pendientes de nota de crédito
- Estadísticas de comprobantes emitidos vs pendientes
- Información para dashboards y alertas

## ⚙️ Configuración

### Variables de Entorno
```env
# Punto de venta para CAE (online)
AFIP_PUNTO_VENTA_CAE=1

# Punto de venta para CAEA (offline)  
AFIP_PUNTO_VENTA_CAEA=2

# URLs de servicios AFIP
AFIP_WSFE_URL=https://wswhomo.afip.gov.ar/wsfev1/service.asmx
AFIP_WSAA_URL=https://wsaahomo.afip.gov.ar/ws/services/LoginCms
```

### Certificados AFIP
- Certificado y clave privada en formato PEM
- Configuración automática desde archivos
- Soporte para ambiente de testing y producción

## 📈 Casos de Uso

### 1. Facturación Normal
```http
POST /api/comprobantes
{
  "ventaId": 123,
  "tipoComprobante": "FACTURA_B",
  "modoGeneracion": "AUTO"
}
```

### 2. Nota de Crédito por Cancelación
```http
POST /api/comprobantes  
{
  "ventaId": 456,
  "tipoComprobante": "NOTA_CREDITO_B",
  "modoGeneracion": "AUTO"
}
```

### 3. Comprobante Offline (Sin Internet)
```http
POST /api/comprobantes
{
  "ventaId": 789,
  "tipoComprobante": "FACTURA_B", 
  "modoGeneracion": "OFFLINE"
}
```

## 🔍 Troubleshooting

### Errores Comunes
1. **"Venta no encontrada"**: Verificar que el `ventaId` existe
2. **"Comprobante ya emitido"**: La venta ya tiene comprobante generado
3. **"Error de AFIP"**: Revisar conectividad y certificados
4. **"CAEA no disponible"**: Verificar códigos CAEA en base de datos

### Logs y Debugging
- Todos los intentos se registran en `comprobante_attempts`
- Logs detallados de comunicación con AFIP
- Endpoint de intentos para análisis: `GET /api/comprobantes/intentos/{ventaId}`
