# 🛒 API de Ventas - Documentación Completa

## 📋 Resumen

El sistema de ventas permite crear, consultar, filtrar y cancelar ventas con arquitectura desacoplada. Soporta múltiples medios de pago, descuentos, integración con usuarios y manejo robusto de errores.

## 🎯 Características Principales

### ✅ **Funcionalidades de Ventas**
- Creación de ventas con múltiples ítems
- Soporte para descuentos a nivel de ítem
- Múltiples medios de pago
- Integración con balanza (códigos de ticket)
- Cancelación de ventas con tracking

### ✅ **Consultas Avanzadas**
- Búsqueda por ID, número de venta, usuario
- Filtros por fecha, medio de pago, comprobante
- Paginación y ordenamiento
- Exclusión/inclusión de ventas canceladas

### ✅ **Arquitectura Desacoplada**
- Separación entre creación de venta y facturación
- Procesamiento asíncrono de comprobantes
- Impresión opcional e independiente

## 🌐 API Endpoints

### Crear Venta
**Endpoint:** `POST /api/sales`

**Request Body:**
```json
{
  "vendedor": "vendedor1",
  "medioPago": "EFECTIVO",
  "porcentajeDescuento": 10.0,
  "codigoTicketBalanza": "TKT001",
  "idTicketBalanza": "ID001",
  "total": 1250.00,
  "items": [
    {
      "productoNombre": "Producto Ejemplo",
      "cantidad": 2.0,
      "precioUnitario": 625.00,
      "subtotal": 1250.00,
      "unidadMedidaId": 1,
      "tipoIvaId": 5
    }
  ],
  "imprimirTicket": true,
  "facturaOnline": false,
  "facturaOffline": false
}
```

**Response:**
```json
{
  "saleId": 456
}
```

### Consultar Venta por ID
**Endpoint:** `GET /api/sales/{ventaId}`

**Response:**
```json
{
  "id": 456,
  "numeroVenta": "V-12345678",
  "fechaVenta": "15/01/2024 14:30",
  "usuarioUsername": "vendedor1",
  "usuarioNombre": "Vendedor 1",
  "montoTotal": "1,250.00",
  "medioPago": "EFECTIVO",
  "porcentajeDescuento": 10.0,
  "comprobanteEmitido": true,
  "cancelada": false,
  "notaCreditoGenerada": false,
  "items": [
    {
      "productoNombre": "Producto Ejemplo",
      "cantidad": 2.0,
      "precioUnitario": "625.00",
      "subtotal": "1,250.00",
      "tipoIvaId": 5
    }
  ]
}
```

### Buscar Ventas con Filtros
**Endpoint:** `GET /api/sales`

**Parámetros de Query:**
- `fechaDesde` (opcional): Fecha desde en formato `yyyy-MM-dd HH:mm:ss`
- `fechaHasta` (opcional): Fecha hasta en formato `yyyy-MM-dd HH:mm:ss`
- `usuarios` (opcional): Lista de usernames (repetir parámetro)
- `comprobanteEmitido` (opcional): `true`, `false` o no especificar
- `mediosPago` (opcional): Lista de medios de pago (repetir parámetro)
- `incluirCanceladas` (opcional): `true`, `false` (default: `false`)
- `page` (opcional): Número de página (default: 1)
- `size` (opcional): Tamaño de página 1-100 (default: 20)

**Medios de Pago Válidos:**
- `EFECTIVO`
- `TARJETA_DEBITO`
- `TARJETA_CREDITO`
- `QR`
- `TRANSFERENCIA`

**Ejemplos:**
```http
# Ventas del día actual
GET /api/sales?fechaDesde=2024-01-15 00:00:00&fechaHasta=2024-01-15 23:59:59

# Ventas de un usuario específico
GET /api/sales?usuarios=vendedor1

# Ventas sin comprobante emitido
GET /api/sales?comprobanteEmitido=false

# Ventas por medio de pago
GET /api/sales?mediosPago=EFECTIVO&mediosPago=TARJETA_CREDITO

# Con paginación
GET /api/sales?page=2&size=50
```

### Otros Endpoints de Consulta

#### Por Número de Venta
**Endpoint:** `GET /api/sales/numero/{numeroVenta}`

#### Por Usuario
**Endpoint:** `GET /api/sales/usuario/{username}`

#### Por Rango de Fechas (POST)
**Endpoint:** `POST /api/sales/buscar-por-fecha`

**Request Body:**
```json
{
  "fechaDesde": "2024-01-01 00:00:00",
  "fechaHasta": "2024-01-31 23:59:59"
}
```

## 🚫 Cancelación de Ventas

### Cancelar Venta
**Endpoint:** `POST /api/sales/{ventaId}/cancel`

**Request Body:**
```json
{
  "usuarioCancelacion": "admin",
  "motivo": "Error en la venta",
  "generarNotaCreditoOnline": true
}
```

**Response:**
```json
{
  "ventaId": 456,
  "cancelada": true,
  "fechaCancelacion": "15/01/2024 15:30",
  "usuarioCancelacion": "admin",
  "motivo": "Error en la venta",
  "notaCreditoGenerada": true,
  "comprobanteNotaCreditoId": 789
}
```

### Características de Cancelación
- **Automática**: Si la venta tiene comprobante, genera automáticamente Nota de Crédito B
- **Consistente**: La nota de crédito usa el mismo método (CAE/CAEA) que el comprobante original
- **Tracking**: Campo `notaCreditoGenerada` trackea el estado
- **Auditoría**: Registra usuario, fecha y motivo de cancelación

## 📊 Estructura de Respuesta Paginada

```json
{
  "content": [
    {
      "id": 456,
      "numeroVenta": "V-12345678",
      // ... resto de campos de venta
    }
  ],
  "page": 1,
  "size": 20,
  "totalElements": 150,
  "totalPages": 8,
  "hasNext": true,
  "hasPrevious": false
}
```

## 💰 Medios de Pago y Descuentos

### Medios de Pago Soportados
- **EFECTIVO** - Pago en efectivo
- **TARJETA_DEBITO** - Tarjeta de débito (electrónico)
- **TARJETA_CREDITO** - Tarjeta de crédito (electrónico)
- **QR** - Código QR (electrónico)
- **TRANSFERENCIA** - Transferencia bancaria (electrónico)

### Descuentos
- **Nivel de ítem**: Se aplica a cada producto individualmente
- **Nivel de venta**: Se aplica a toda la venta
- **Cálculo automático**: Base imponible e IVA se calculan automáticamente
- **Validación**: Descuentos entre 0% y 100%

## 🔧 Integración con Otros Sistemas

### Balanza
- `codigoTicketBalanza`: Código del ticket de la balanza
- `idTicketBalanza`: ID único del ticket
- Opcional, para trazabilidad

### Impresión
- `imprimirTicket`: Controla si se imprime ticket automáticamente
- Procesamiento asíncrono, no bloquea la venta
- Manejo robusto de errores de impresora

### Facturación
- `facturaOnline`: Genera comprobante online (CAE)
- `facturaOffline`: Genera comprobante offline (CAEA)
- Procesamiento asíncrono, no bloquea la venta
- `facturaOnline` tiene precedencia sobre `facturaOffline`

## 🚨 Manejo de Errores

### Códigos de Error Comunes
- **400 Bad Request**: Parámetros inválidos
- **404 Not Found**: Venta no encontrada
- **409 Conflict**: Venta ya cancelada
- **422 Unprocessable Entity**: Datos de negocio inválidos

### Validaciones
- Usuario debe existir en el sistema
- Items no pueden estar vacíos
- Cantidades y precios deben ser positivos
- Medios de pago deben ser válidos
- Descuentos entre 0% y 100%

## 📈 Casos de Uso Comunes

### 1. Venta Simple
```http
POST /api/sales
{
  "vendedor": "vendedor1",
  "medioPago": "EFECTIVO",
  "total": 100.00,
  "items": [
    {
      "productoNombre": "Producto A",
      "cantidad": 1.0,
      "precioUnitario": 100.00,
      "subtotal": 100.00,
      "unidadMedidaId": 1,
      "tipoIvaId": 5
    }
  ]
}
```

### 2. Venta con Descuento y Facturación
```http
POST /api/sales
{
  "vendedor": "vendedor1",
  "medioPago": "TARJETA_CREDITO",
  "porcentajeDescuento": 15.0,
  "total": 850.00,
  "items": [...],
  "imprimirTicket": true,
  "facturaOnline": true
}
```

### 3. Consulta de Ventas del Día
```http
GET /api/sales?fechaDesde=2024-01-15 00:00:00&fechaHasta=2024-01-15 23:59:59&page=1&size=50
```
