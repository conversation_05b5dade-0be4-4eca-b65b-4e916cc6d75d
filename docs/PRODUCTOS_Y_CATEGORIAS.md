# 📦 Productos y Categorías - Documentación API

## 📋 Resumen

Sistema de gestión de productos y categorías con soporte para actualizaciones masivas, ordenamiento personalizado, colores de categorías y manejo de productos sin categoría.

## 🏷️ Categorías

### Características
- **Ordenamiento personalizado**: Campo `orden` para controlar la secuencia de visualización
- **Colores personalizados**: Campo `color` en formato hexadecimal (rrggbb)
- **Categoría especial**: "Sin Categoría" para productos no categorizados
- **Actualizaciones masivas**: Endpoint para actualizar múltiples categorías

### API Endpoints

#### Listar Categorías
**Endpoint:** `GET /api/categorias`

**Response:**
```json
[
  {
    "id": 1,
    "nombre": "Bebidas",
    "descripcion": "Bebidas y refrescos",
    "orden": 1,
    "color": "ff5733",
    "activo": true
  },
  {
    "id": 2,
    "nombre": "Snacks",
    "descripcion": "Aperitivos y snacks",
    "orden": 2,
    "color": "33ff57",
    "activo": true
  }
]
```

#### Crear Categoría
**Endpoint:** `POST /api/categorias`

**Request Body:**
```json
{
  "nombre": "Nueva Categoría",
  "descripcion": "Descripción opcional",
  "orden": 5,
  "color": "3357ff"
}
```

#### Actualizar Categoría
**Endpoint:** `PUT /api/categorias/{id}`

**Request Body:**
```json
{
  "nombre": "Categoría Actualizada",
  "descripcion": "Nueva descripción",
  "orden": 3,
  "color": "ff3357"
}
```

#### Actualización Masiva
**Endpoint:** `PUT /api/categorias/bulk`

**Request Body:**
```json
[
  {
    "id": 1,
    "orden": 2,
    "color": "ff0000"
  },
  {
    "id": 2,
    "orden": 1,
    "color": "00ff00"
  }
]
```

**Características de Actualización Masiva:**
- Actualiza solo los campos proporcionados
- Campos omitidos permanecen sin cambios
- Valores especiales para limpiar campos:
  - `orden < 0`: Limpia el campo orden (null)
  - `color = ""`: Limpia el color (null)

#### Eliminar Categoría
**Endpoint:** `DELETE /api/categorias/{id}`

**Comportamiento:**
- **Hard delete**: Elimina completamente el registro
- **Reasignación automática**: Productos de la categoría eliminada se asignan a "Sin Categoría"
- **Validación**: No permite eliminar la categoría "Sin Categoría" (ID especial)

### Categoría "Sin Categoría"

#### Características Especiales
- **ID fijo**: Siempre tiene ID = -1
- **Creación automática**: Se crea automáticamente si no existe
- **No eliminable**: Protegida contra eliminación
- **Reasignación automática**: Productos huérfanos se asignan aquí

#### Implementación
```sql
-- Se crea automáticamente con:
INSERT INTO categorias (id, nombre, descripcion, orden, activo) 
VALUES (-1, 'Sin Categoría', 'Productos sin categoría asignada', 999, true)
```

### Colores de Categorías

#### Formato
- **Hexadecimal**: 6 caracteres (rrggbb)
- **Sin prefijo**: No incluir '#'
- **Ejemplos válidos**: `ff5733`, `33ff57`, `3357ff`
- **Validación**: Formatos inválidos se guardan como NULL

#### Uso
```json
{
  "color": "ff5733"  // Naranja
}
```

## 📦 Productos

### Características
- **Sincronización externa**: Soporte para sincronización con sistemas externos
- **Actualizaciones masivas**: Endpoint para actualizar múltiples productos
- **Gestión de categorías**: Asignación automática a "Sin Categoría"
- **Tipos de IVA**: Soporte para diferentes tipos de IVA

### API Endpoints

#### Listar Productos
**Endpoint:** `GET /api/productos`

**Parámetros:**
- `categoriaId` (opcional): Filtrar por categoría
- `activo` (opcional): Filtrar por estado activo

**Response:**
```json
[
  {
    "id": 1,
    "codigo": "PROD001",
    "nombre": "Coca Cola 500ml",
    "descripcion": "Bebida gaseosa",
    "precioUnitario": 150.00,
    "unidadMedidaId": 2,
    "categoriaId": 1,
    "tipoIvaId": 5,
    "activo": true
  }
]
```

#### Crear Producto
**Endpoint:** `POST /api/productos`

**Request Body:**
```json
{
  "codigo": "PROD002",
  "nombre": "Nuevo Producto",
  "descripcion": "Descripción del producto",
  "precioUnitario": 200.00,
  "unidadMedidaId": 1,
  "categoriaId": 2,
  "tipoIvaId": 5
}
```

#### Actualizar Producto
**Endpoint:** `PUT /api/productos/{id}`

#### Actualización Masiva
**Endpoint:** `PUT /api/productos/bulk`

**Request Body:**
```json
[
  {
    "id": 1,
    "precioUnitario": 175.00,
    "categoriaId": 2
  },
  {
    "id": 2,
    "categoriaId": -1,
    "activo": false
  }
]
```

**Valores Especiales:**
- `categoriaId < 0`: Asigna a "Sin Categoría"
- `stockActual < 0`: Limpia el stock (null)

#### Eliminar Producto
**Endpoint:** `DELETE /api/productos/{id}`

**Comportamiento:**
- **Hard delete**: Elimina completamente el registro
- **Validación**: Verifica que no esté en uso en ventas activas

### Sincronización Externa

#### Endpoint de Sincronización
**Endpoint:** `POST /api/productos/sync`

**Request Body:**
```json
[
  {
    "product_id": "EXT001",
    "name": "Producto Externo",
    "description": "Descripción",
    "price": 250.00,
    "uom_id": "kg",
    "isactive": true
  }
]
```

**Mapeo de Campos:**
- `product_id` → `codigo`
- `name` → `nombre` y `descripcion`
- `price` → `precioUnitario`
- `uom_id` → `unidadMedidaId` (con mapeo: 'kg'=1, 'un'=2)
- `isactive` → `activo`
- `tipoIvaId` → Siempre 5 (IVA 21%)

## 🔧 Unidades de Medida

### Mapeo Estándar
- **ID 1**: Kilogramo (kg)
- **ID 2**: Unidad (un)

### Uso en Sincronización
```json
{
  "uom_id": "kg"  // Se mapea a unidadMedidaId = 1
}
```

## 🎨 Ejemplos de Uso

### 1. Crear Categoría con Color
```http
POST /api/categorias
{
  "nombre": "Lácteos",
  "descripcion": "Productos lácteos",
  "orden": 3,
  "color": "87ceeb"
}
```

### 2. Reordenar Categorías
```http
PUT /api/categorias/bulk
[
  {"id": 1, "orden": 3},
  {"id": 2, "orden": 1},
  {"id": 3, "orden": 2}
]
```

### 3. Actualizar Precios Masivamente
```http
PUT /api/productos/bulk
[
  {"id": 1, "precioUnitario": 180.00},
  {"id": 2, "precioUnitario": 220.00},
  {"id": 3, "precioUnitario": 95.00}
]
```

### 4. Mover Productos a Sin Categoría
```http
PUT /api/productos/bulk
[
  {"id": 5, "categoriaId": -1},
  {"id": 8, "categoriaId": -1}
]
```

## 🚨 Validaciones y Errores

### Categorías
- **Nombre único**: No se permiten nombres duplicados
- **Color válido**: Formato hexadecimal de 6 caracteres
- **Orden positivo**: Valores negativos limpian el campo

### Productos
- **Código único**: No se permiten códigos duplicados
- **Precio positivo**: Precios deben ser mayores a 0
- **Categoría válida**: Debe existir o ser -1 para "Sin Categoría"
- **Tipo IVA válido**: Debe corresponder a un tipo existente

### Eliminación
- **Categorías en uso**: Se permite eliminar, productos se reasignan
- **Productos en ventas**: Se valida antes de eliminar
- **Sin Categoría**: Protegida contra eliminación
