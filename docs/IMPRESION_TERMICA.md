# 🖨️ Impresión Térmica - Documentación Completa

## 📋 Resumen

Sistema de impresión térmica con soporte para tickets fiscales y no fiscales, estilos ESC/POS, códigos QR fiscales y manejo robusto de errores de impresora.

## 🎯 Características Principales

### ✅ **Tipos de Tickets**
- **Tickets Fiscales**: Con formato AFIP, QR fiscal y datos de comprobante
- **Tickets No Fiscales**: Para ventas sin comprobante
- **Reportes de Ventas**: Resúmenes y estadísticas impresas

### ✅ **Estilos ESC/POS**
- **Negrita**: Para títulos y elementos importantes
- **Subrayado**: Para separadores y énfasis
- **Tamaños de fuente**: Normal, doble ancho, doble alto
- **Alineación**: Izquierda, centro, derecha
- **Separadores**: Líneas punteadas y continuas

### ✅ **Códigos QR Fiscales**
- **Cumplimiento AFIP**: Según especificaciones ARCA
- **Campos requeridos**: Fecha emisión, CUIT, datos del comprobante
- **Códigos de autorización**: CAE/CAEA según corresponda

## 🌐 API Endpoints

### Imprimir Ticket de Venta
**Endpoint:** `POST /api/print/ticket/{ventaId}`

**Parámetros:**
- `ventaId`: ID de la venta a imprimir

**Response:**
```json
{
  "success": true,
  "message": "Ticket impreso correctamente",
  "ticketType": "FISCAL"
}
```

### Imprimir Reporte de Ventas
**Endpoint:** `POST /api/print/reporte-ventas`

**Parámetros de Query:**
- `periodo` (opcional): `today`, `yesterday`, `current_week`, `current_month`
- `fechaDesde` (opcional): Fecha inicio (ISO)
- `fechaHasta` (opcional): Fecha fin (ISO)
- `usuarios` (opcional): Lista de usuarios (separados por coma)
- `mediosPago` (opcional): Lista de medios de pago (separados por coma)
- `soloConComprobante` (opcional): `true`, `false`
- `soloMediosPagoElectronicos` (opcional): `true`, `false`

**Response:**
```json
{
  "success": true,
  "message": "Reporte impreso correctamente",
  "totalVentas": 25,
  "montoTotal": 15750.00
}
```

## 🎨 Formato de Tickets

### Ticket Fiscal (Con Comprobante)
```
================================
        NOMBRE COMERCIAL
        Dirección Fiscal
        CUIT: 20-12345678-9
================================

FACTURA B
Nro: 00001-00000123
Fecha: 15/01/2024 14:30
CAE: 74251234567890
Venc: 25/01/2024

A CONSUMIDOR FINAL

--------------------------------
DETALLE DE PRODUCTOS
--------------------------------
Producto Ejemplo
  2.00 x $625.00        $1,250.00

--------------------------------
SUBTOTAL              $1,250.00
DESCUENTO (10%)        ($125.00)
TOTAL                 $1,125.00

Medio de Pago: EFECTIVO

[QR CODE FISCAL]

CAE: 74251234567890
Venc CAE: 25/01/2024
================================
```

### Ticket No Fiscal (Sin Comprobante)
```
================================
        NOMBRE COMERCIAL
        Dirección Fiscal
================================

TICKET NO FISCAL
Nro Venta: V-12345678
Fecha: 15/01/2024 14:30

--------------------------------
DETALLE DE PRODUCTOS
--------------------------------
Producto Ejemplo
  2.00 x $625.00        $1,250.00

--------------------------------
SUBTOTAL              $1,250.00
DESCUENTO (10%)        ($125.00)
TOTAL                 $1,125.00

Medio de Pago: EFECTIVO

¡Gracias por su compra!
================================
```

## 🔧 Configuración de Impresora

### Variables de Entorno
```env
# Configuración de impresora
PRINTER_NAME=POS-80
PRINTER_CONNECTION_TYPE=USB
PRINTER_TIMEOUT_MS=5000

# Configuración de empresa (para tickets)
EMPRESA_NOMBRE=Mi Comercio
EMPRESA_DIRECCION=Av. Principal 123
EMPRESA_CUIT=20-12345678-9
```

### Tipos de Conexión Soportados
- **USB**: Conexión directa por USB
- **Network**: Conexión por red (IP)
- **Bluetooth**: Conexión inalámbrica

## 📊 QR Fiscal AFIP

### Especificación ARCA
El código QR fiscal debe incluir los siguientes campos según AFIP:

```json
{
  "ver": 1,
  "fecha": "2024-01-15",
  "cuit": 20123456789,
  "ptoVta": 1,
  "tipoCmp": 6,
  "nroCmp": 123,
  "importe": 1125.00,
  "moneda": "PES",
  "ctz": 1,
  "tipoDocRec": 99,
  "nroDocRec": 0,
  "tipoCodAut": "E",
  "codAut": 74251234567890
}
```

### Campos Explicados
- `ver`: Versión del formato (siempre 1)
- `fecha`: Fecha de emisión (YYYY-MM-DD)
- `cuit`: CUIT del emisor
- `ptoVta`: Punto de venta
- `tipoCmp`: Tipo de comprobante (6 = Factura B)
- `nroCmp`: Número de comprobante
- `importe`: Importe total
- `moneda`: Moneda (PES = Pesos)
- `ctz`: Cotización (1 para pesos)
- `tipoDocRec`: Tipo documento receptor (99 = Consumidor Final)
- `nroDocRec`: Número documento receptor (0 para CF)
- `tipoCodAut`: Tipo código autorización (E = CAE, A = CAEA)
- `codAut`: Código de autorización

## 🚨 Manejo de Errores

### Errores Comunes de Impresora
1. **Impresora no conectada**
   - Error: `PrinterNotConnectedException`
   - Solución: Verificar conexión física/red

2. **Sin papel**
   - Error: `OutOfPaperException`
   - Solución: Recargar papel en la impresora

3. **Timeout de conexión**
   - Error: `PrinterTimeoutException`
   - Solución: Verificar configuración de timeout

4. **Impresora ocupada**
   - Error: `PrinterBusyException`
   - Solución: Esperar y reintentar

### Manejo Robusto
- **No bloquea la aplicación**: Los errores de impresión no afectan la venta
- **Logs detallados**: Registro completo de errores para debugging
- **Reintentos automáticos**: Hasta 3 intentos con delay progresivo
- **Fallback graceful**: La aplicación continúa funcionando sin impresora

## 🎯 Estilos ESC/POS Implementados

### Comandos de Formato
```kotlin
// Negrita
printer.bold(true)
printer.print("TEXTO EN NEGRITA")
printer.bold(false)

// Subrayado
printer.underline(true)
printer.print("TEXTO SUBRAYADO")
printer.underline(false)

// Tamaño doble
printer.doubleWidth(true)
printer.print("TEXTO ANCHO")
printer.doubleWidth(false)

// Alineación
printer.align(Align.CENTER)
printer.print("TEXTO CENTRADO")
printer.align(Align.LEFT)
```

### Separadores Estilizados
```
================================  (Línea doble)
--------------------------------  (Línea simple)
................................  (Línea punteada)
```

## 📈 Casos de Uso

### 1. Impresión Automática en Venta
```http
POST /api/sales
{
  "vendedor": "vendedor1",
  "items": [...],
  "imprimirTicket": true,  // Imprime automáticamente
  "facturaOnline": true
}
```

### 2. Reimpresión de Ticket
```http
POST /api/print/ticket/123
```

### 3. Reporte Diario
```http
POST /api/print/reporte-ventas?periodo=today
```

### 4. Reporte Personalizado
```http
POST /api/print/reporte-ventas?fechaDesde=2024-01-01T00:00:00&fechaHasta=2024-01-31T23:59:59&usuarios=vendedor1,vendedor2
```

## 🔍 Troubleshooting

### Problemas Comunes

#### Impresora No Responde
1. Verificar conexión física
2. Revisar configuración en `.env`
3. Comprobar drivers instalados
4. Verificar permisos de acceso

#### Formato Incorrecto
1. Verificar codificación de caracteres
2. Comprobar ancho de papel (80mm estándar)
3. Revisar configuración ESC/POS

#### QR No Se Genera
1. Verificar datos del comprobante
2. Comprobar formato JSON del QR
3. Revisar especificaciones AFIP ARCA

### Logs de Debugging
```
[PRINTER] Iniciando impresión de ticket para venta 123
[PRINTER] Conectando a impresora POS-80
[PRINTER] Generando QR fiscal con CAE 74251234567890
[PRINTER] Enviando comandos ESC/POS
[PRINTER] Impresión completada exitosamente
```

## ⚙️ Configuración Avanzada

### Personalización de Formato
- **Ancho de línea**: Configurable según impresora
- **Márgenes**: Ajustables para diferentes tamaños
- **Fuentes**: Soporte para fuentes A y B
- **Códigos de barras**: Soporte para diferentes tipos

### Optimización de Performance
- **Pool de conexiones**: Reutilización de conexiones
- **Cache de comandos**: Optimización de comandos ESC/POS
- **Compresión de datos**: Para impresoras de red

### Integración con Hardware
- **Cajón de dinero**: Apertura automática
- **Display de cliente**: Mostrar totales
- **Lector de códigos**: Integración con scanners
